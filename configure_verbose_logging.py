#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
详细日志配置脚本
临时调整日志配置以显示更多详细信息
"""
import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

from loguru import logger
from core.logger import log


def configure_verbose_logging():
    """配置详细日志输出"""
    try:
        print("🔧 配置详细日志输出...")
        
        # 移除现有的处理器
        logger.remove()
        
        # 添加详细的控制台处理器
        logger.add(
            sys.stdout,
            format="<green>{time:HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> | <level>{message}</level>",
            level="DEBUG",  # 显示所有级别的日志
            colorize=True,  # 启用颜色
            catch=True,
            backtrace=False,
            diagnose=False
        )
        
        # 添加文件处理器（可选）
        logger.add(
            "logs/verbose_test.log",
            format="{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} | {message}",
            level="DEBUG",
            rotation="10 MB",
            retention="1 day",
            encoding="utf-8",
            catch=True,
            backtrace=False,
            diagnose=False
        )
        
        print("✅ 详细日志配置完成")
        print("💡 现在所有级别的日志都会在控制台显示")
        
        # 测试不同级别的日志
        logger.debug("🐛 DEBUG: 这是调试信息")
        logger.info("ℹ️ INFO: 这是普通信息")
        logger.warning("⚠️ WARNING: 这是警告信息")
        logger.error("❌ ERROR: 这是错误信息")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置详细日志失败: {e}")
        return False


def test_floating_command_with_verbose_logs():
    """使用详细日志测试浮窗命令"""
    logger.info("🚀 开始使用详细日志测试浮窗命令")
    
    try:
        from pages.apps.ella.floating_page import EllaFloatingPage
        
        floating_page = EllaFloatingPage()
        
        # 检查浮窗状态
        logger.info("检查浮窗状态...")
        is_visible = floating_page.is_floating_window_visible()
        logger.info(f"浮窗可见性: {is_visible}")
        
        if not is_visible:
            logger.info("尝试唤起浮窗...")
            success = floating_page.trigger_ella_by_power_key(duration=3.0)
            logger.info(f"浮窗唤起结果: {success}")
            
            if not success:
                logger.error("无法唤起浮窗")
                return False
        
        # 执行命令
        test_command = "hello"
        logger.info(f"执行测试命令: {test_command}")
        
        success = floating_page.execute_text_command_in_floating(test_command)
        logger.info(f"命令执行结果: {success}")
        
        return success
        
    except Exception as e:
        logger.error(f"测试异常: {e}")
        return False


def restore_normal_logging():
    """恢复正常的日志配置"""
    try:
        print("\n🔄 恢复正常日志配置...")
        
        # 重新初始化日志系统
        from core.logger import Logger
        Logger._initialized = False
        logger_instance = Logger()
        
        print("✅ 日志配置已恢复")
        return True
        
    except Exception as e:
        print(f"❌ 恢复日志配置失败: {e}")
        return False


def main():
    """主函数"""
    print("🚀 详细日志配置和测试脚本")
    print("=" * 50)
    
    try:
        # 1. 配置详细日志
        if not configure_verbose_logging():
            return False
        
        print("\n" + "=" * 50)
        input("按回车键继续测试浮窗命令...")
        
        # 2. 测试浮窗命令
        logger.info("开始测试浮窗命令...")
        success = test_floating_command_with_verbose_logs()
        
        if success:
            logger.info("✅ 浮窗命令测试成功")
        else:
            logger.error("❌ 浮窗命令测试失败")
        
        print("\n" + "=" * 50)
        input("按回车键恢复正常日志配置...")
        
        # 3. 恢复正常日志配置
        restore_normal_logging()
        
        return success
        
    except KeyboardInterrupt:
        print("\n⚠️ 测试被用户中断")
        return False
    except Exception as e:
        print(f"\n❌ 测试异常: {e}")
        return False


if __name__ == '__main__':
    try:
        success = main()
        if success:
            print("\n🎉 详细日志测试完成！")
        else:
            print("\n❌ 详细日志测试失败！")
            sys.exit(1)
    except Exception as e:
        print(f"\n❌ 脚本运行异常: {e}")
        sys.exit(1)
