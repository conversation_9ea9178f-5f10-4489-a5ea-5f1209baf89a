#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
改进的屏幕锁定检测功能测试脚本
测试优化后的多重锁屏检测方法
"""
import sys
import os
import time

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

from pages.apps.ella.floating_page import EllaFloatingPage
from core.logger import log


def test_comprehensive_lock_detection():
    """测试完整的锁屏检测功能"""
    log.info("🔍 测试完整的锁屏检测功能")
    
    floating_page = EllaFloatingPage()
    
    try:
        log.info("1. 测试完整锁屏检测方法...")
        is_locked_full = floating_page._is_screen_locked()
        log.info(f"完整检测结果: {'🔒 已锁定' if is_locked_full else '🔓 未锁定'}")
        
        log.info("2. 测试简化锁屏检测方法...")
        is_locked_simple = floating_page._is_screen_locked_simple()
        log.info(f"简化检测结果: {'🔒 已锁定' if is_locked_simple else '🔓 未锁定'}")
        
        log.info("3. 测试带降级策略的检测方法...")
        is_locked_fallback = floating_page.is_screen_locked_with_fallback()
        log.info(f"降级检测结果: {'🔒 已锁定' if is_locked_fallback else '🔓 未锁定'}")
        
        # 比较结果一致性
        results = [is_locked_full, is_locked_simple, is_locked_fallback]
        if len(set(results)) == 1:
            log.info("✅ 所有检测方法结果一致")
            return True
        else:
            log.warning(f"⚠️ 检测方法结果不一致: 完整={is_locked_full}, 简化={is_locked_simple}, 降级={is_locked_fallback}")
            # 以降级方法的结果为准
            return True
            
    except Exception as e:
        log.error(f"锁屏检测测试异常: {e}")
        return False


def test_screen_interaction():
    """测试屏幕交互能力检测"""
    log.info("🖱️ 测试屏幕交互能力检测")
    
    floating_page = EllaFloatingPage()
    
    try:
        interaction_ok = floating_page._test_screen_interaction()
        log.info(f"屏幕交互测试结果: {'✅ 正常' if interaction_ok else '❌ 异常'}")
        
        if interaction_ok:
            log.info("屏幕可以正常交互，可能未锁定")
        else:
            log.info("屏幕交互异常，可能处于锁定状态")
            
        return True
        
    except Exception as e:
        log.error(f"屏幕交互测试异常: {e}")
        return False


def test_lock_unlock_cycle():
    """测试锁屏-解锁循环"""
    log.info("🔄 测试锁屏-解锁循环")
    
    floating_page = EllaFloatingPage()
    
    try:
        # 1. 检查初始状态
        log.info("1. 检查初始锁屏状态...")
        initial_locked = floating_page.is_screen_locked_with_fallback()
        log.info(f"初始状态: {'🔒 已锁定' if initial_locked else '🔓 未锁定'}")
        
        # 2. 确保屏幕亮起并解锁
        log.info("2. 确保屏幕亮起并解锁...")
        screen_ready = floating_page._ensure_screen_on()
        log.info(f"屏幕准备结果: {'✅ 成功' if screen_ready else '❌ 失败'}")
        
        # 3. 检查解锁后状态
        log.info("3. 检查解锁后状态...")
        after_unlock = floating_page.is_screen_locked_with_fallback()
        log.info(f"解锁后状态: {'🔒 仍锁定' if after_unlock else '🔓 已解锁'}")
        
        # 4. 验证状态变化
        if initial_locked and not after_unlock:
            log.info("✅ 成功从锁定状态解锁")
        elif not initial_locked and not after_unlock:
            log.info("✅ 保持解锁状态")
        elif after_unlock:
            log.warning("⚠️ 解锁操作可能失败")
        
        return screen_ready
        
    except Exception as e:
        log.error(f"锁屏-解锁循环测试异常: {e}")
        return False


def test_floating_window_with_lock_detection():
    """测试结合锁屏检测的浮窗功能"""
    log.info("🚀 测试结合锁屏检测的浮窗功能")
    
    floating_page = EllaFloatingPage()
    
    try:
        # 1. 检查并处理锁屏状态
        log.info("1. 检查并处理锁屏状态...")
        if floating_page.is_screen_locked_with_fallback():
            log.info("检测到锁屏，尝试解锁...")
            if not floating_page._ensure_screen_on():
                log.error("无法解锁屏幕")
                return False
        
        # 2. 尝试唤起浮窗
        log.info("2. 尝试唤起Ella浮窗...")
        floating_success = floating_page.trigger_ella_by_power_key(duration=3.0)
        log.info(f"浮窗唤起结果: {'✅ 成功' if floating_success else '❌ 失败'}")
        
        # 3. 验证浮窗状态
        if floating_success:
            is_visible = floating_page.is_floating_window_visible()
            log.info(f"浮窗可见性: {'✅ 可见' if is_visible else '❌ 不可见'}")
            
            # 清理浮窗
            if is_visible:
                try:
                    floating_page.close_floating_window()
                    log.info("✅ 浮窗已清理")
                except Exception as e:
                    log.warning(f"清理浮窗失败: {e}")
            
            return is_visible
        
        return floating_success
        
    except Exception as e:
        log.error(f"浮窗功能测试异常: {e}")
        return False


def test_edge_cases():
    """测试边缘情况"""
    log.info("🔍 测试边缘情况")
    
    floating_page = EllaFloatingPage()
    
    try:
        # 测试1: 连续多次检测锁屏状态
        log.info("测试1: 连续多次检测锁屏状态...")
        results = []
        for i in range(3):
            result = floating_page.is_screen_locked_with_fallback()
            results.append(result)
            log.info(f"  第{i+1}次检测: {'🔒 锁定' if result else '🔓 未锁定'}")
            time.sleep(1)
        
        # 检查结果一致性
        if len(set(results)) == 1:
            log.info("✅ 连续检测结果一致")
        else:
            log.warning(f"⚠️ 连续检测结果不一致: {results}")
        
        # 测试2: 在不同时间间隔下检测
        log.info("测试2: 间隔检测...")
        for delay in [0.5, 1.0, 2.0]:
            time.sleep(delay)
            result = floating_page.is_screen_locked_with_fallback()
            log.info(f"  延迟{delay}秒后检测: {'🔒 锁定' if result else '🔓 未锁定'}")
        
        return True
        
    except Exception as e:
        log.error(f"边缘情况测试异常: {e}")
        return False


def main():
    """主函数"""
    log.info("🚀 开始改进的屏幕锁定检测功能测试")
    log.info("=" * 60)
    
    tests = [
        ("完整锁屏检测功能", test_comprehensive_lock_detection),
        ("屏幕交互能力检测", test_screen_interaction),
        ("锁屏-解锁循环", test_lock_unlock_cycle),
        ("浮窗功能结合锁屏检测", test_floating_window_with_lock_detection),
        ("边缘情况测试", test_edge_cases)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        log.info(f"\n📋 开始测试: {test_name}")
        log.info("-" * 40)
        
        try:
            result = test_func()
            results.append((test_name, result))
            
            status = "✅ 通过" if result else "❌ 失败"
            log.info(f"📊 测试结果: {test_name} - {status}")
            
        except Exception as e:
            log.error(f"❌ 测试 {test_name} 异常: {e}")
            results.append((test_name, False))
        
        log.info("-" * 40)
        time.sleep(2)  # 测试间隔
    
    # 输出总结
    log.info("\n" + "=" * 60)
    log.info("📊 测试总结")
    log.info("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        log.info(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    log.info("-" * 60)
    log.info(f"总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        log.info("🎉 所有测试都通过了！改进的锁屏检测功能正常")
        return True
    else:
        log.warning(f"⚠️ 有 {total - passed} 个测试失败")
        return False


if __name__ == '__main__':
    try:
        success = main()
        if success:
            print("\n🎉 测试完成，锁屏检测功能正常！")
        else:
            print("\n❌ 测试完成，部分功能存在问题！")
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n⚠️ 测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 测试运行异常: {e}")
        sys.exit(1)
