"""
AI Gallery与Ella浮窗集成测试
基于Page Object模式的Pytest脚本，测试AI Gallery选择图片后在Ella浮窗中输入指令的完整流程
"""
import pytest
import allure
import time
from core.logger import log


@allure.epic("AI Gallery与Ella集成")
@allure.feature("图片选择与浮窗指令")
class TestAiGalleryEllaIntegration:
    """AI Gallery与Ella浮窗集成测试类"""

    @pytest.fixture(scope="function")
    def ai_gallery_page(self):
        """AI Gallery页面fixture"""
        from pages.apps.ai_gallery.photos_page import AiGalleryPhotosPage

        page = AiGalleryPhotosPage()

        try:
            # 启动AI Gallery应用
            assert page.start_app(), "AI Gallery应用启动失败"
            assert page.wait_for_page_load(timeout=15), "AI Gallery页面加载失败"

            log.info("✅ AI Gallery应用启动成功")
            yield page

        except Exception as e:
            log.error(f"❌ AI Gallery应用启动异常: {e}")
            pytest.fail(f"AI Gallery应用启动异常: {e}")
        finally:
            # 清理
            try:
                page.stop_app()
            except Exception as e:
                log.warning(f"⚠️ 停止AI Gallery应用异常: {e}")

    @pytest.fixture(scope="function")
    def ella_floating_page(self):
        """Ella浮窗页面fixture"""
        from pages.apps.ella.floating_page import EllaFloatingPage

        page = EllaFloatingPage()

        try:
            log.info("✅ Ella浮窗页面初始化成功")
            yield page

        except Exception as e:
            log.error(f"❌ Ella浮窗页面初始化异常: {e}")
            pytest.fail(f"Ella浮窗页面初始化异常: {e}")
        finally:
            # 清理：关闭浮窗
            try:
                if page.is_floating_window_visible():
                    page.close_floating_window()
            except Exception as e:
                log.warning(f"⚠️ 关闭Ella浮窗异常: {e}")

    def _take_screenshot(self, page_instance, name: str):
        """截图并附加到Allure报告"""
        try:
            screenshot_path = page_instance.screenshot(f"{name}.png")
            allure.attach.file(screenshot_path, name=name, attachment_type=allure.attachment_type.PNG)
            log.debug(f"截图已保存: {name}")
        except Exception as e:
            log.warning(f"截图失败: {e}")

    def _create_test_summary(self, command: str, response_text: str) -> str:
        """创建测试总结"""
        summary = f"""
AI Gallery与Ella浮窗集成测试总结
=====================================

测试步骤:
1. ✅ 打开AI Gallery应用
2. ✅ 确保在Photos页面
3. ✅ 检查并选择第一张图片
4. ✅ 打开Ella浮窗
5. ✅ 在浮窗中输入指令

执行命令: {command}
Ella响应: {response_text}
测试结果: 成功

测试时间: {time.strftime('%Y-%m-%d %H:%M:%S')}
"""
        return summary.strip()
