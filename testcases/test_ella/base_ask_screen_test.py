"""
Ella浮窗Ask Screen测试基类
基于SimpleEllaTest设计模式，提供浮窗中输入指令和断言结果的封装
修复缓冲区分离问题的优化版本
"""
import pytest
import allure
import time
import sys
import io
import threading
from typing import List, Optional, Union
from testcases.test_ella.base_ella_test import BaseEllaTest
from pages.apps.ella.floating_page import EllaFloatingPage

# 安全的日志导入和初始化
try:
    from core.logger import log
except Exception as e:
    # 如果日志系统有问题，创建一个安全的备用日志
    import logging
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
    log = logging.getLogger(__name__)


class SafeLogger:
    """
    安全的日志处理器，防止缓冲区分离错误
    """

    def __init__(self):
        self._lock = threading.Lock()
        self._fallback_stream = io.StringIO()

    def _safe_log(self, level: str, message: str):
        """安全的日志记录方法"""
        try:
            with self._lock:
                # 尝试使用原始日志系统
                if hasattr(log, level):
                    getattr(log, level)(str(message))
                else:
                    # 备用方案：直接打印
                    print(f"[{level.upper()}] {message}")
        except (ValueError, OSError, AttributeError) as e:
            # 处理缓冲区分离错误
            try:
                # 尝试写入到备用流
                self._fallback_stream.write(f"[{level.upper()}] {message}\n")
                # 尝试输出到可用的流
                for stream in [sys.stdout, sys.stderr]:
                    try:
                        if hasattr(stream, 'write') and not stream.closed:
                            stream.write(f"[{level.upper()}] {message}\n")
                            stream.flush()
                            break
                    except (ValueError, OSError):
                        continue
            except Exception:
                # 完全静默处理
                pass
        except Exception:
            # 其他异常也静默处理
            pass

    def info(self, message: str):
        """记录INFO级别日志"""
        self._safe_log('info', message)

    def warning(self, message: str):
        """记录WARNING级别日志"""
        self._safe_log('warning', message)

    def error(self, message: str):
        """记录ERROR级别日志"""
        self._safe_log('error', message)

    def debug(self, message: str):
        """记录DEBUG级别日志"""
        self._safe_log('debug', message)


# 创建安全日志实例
safe_log = SafeLogger()


class BaseAskScreenTest(BaseEllaTest):
    """
    Ella浮窗Ask Screen测试基类
    继承自BaseEllaTest，专门用于浮窗相关的测试
    """

    # 浮窗相关配置
    _floating_window_timeout = 10  # 浮窗操作超时时间
    _response_wait_timeout = 8     # 响应等待超时时间
    _command_retry_count = 2       # 命令执行重试次数

    @pytest.fixture(scope="function")
    def ella_floating_page(self):
        """Ella浮窗页面fixture - 使用安全日志"""
        try:
            # 在第一个测试用例开始前设置屏幕
            self.setup_batch_test_screen()

            floating_page = EllaFloatingPage()
            safe_log.info("✅ Ella浮窗页面初始化成功")
            yield floating_page

        except Exception as e:
            safe_log.error(f"❌ Ella浮窗页面初始化异常: {e}")
            pytest.fail(f"Ella浮窗页面初始化异常: {e}")
        finally:
            # 清理：关闭浮窗
            try:
                if floating_page.is_floating_window_visible():
                    floating_page.close_floating_window()
                    safe_log.info("✅ Ella浮窗已关闭")
            except Exception as e:
                safe_log.warning(f"⚠️ 关闭Ella浮窗异常: {e}")

    def ensure_floating_window_ready(self, floating_page: EllaFloatingPage) -> bool:
        """
        确保浮窗就绪 - 使用安全日志

        Args:
            floating_page: Ella浮窗页面实例

        Returns:
            bool: 浮窗是否就绪
        """
        try:
            safe_log.info("确保Ella浮窗就绪...")

            # 检查浮窗是否已经可见
            if floating_page.is_floating_window_visible():
                safe_log.info("✅ 浮窗已可见")
                return floating_page.ensure_floating_window_ready()

            # 尝试通过长按power键唤起浮窗
            safe_log.info("尝试通过长按power键唤起浮窗...")
            if floating_page.trigger_ella_by_power_key(duration=3.0):
                safe_log.info("✅ 通过长按power键成功唤起浮窗")
                return floating_page.ensure_floating_window_ready()

            # 备选方案：使用其他方式打开浮窗
            safe_log.warning("长按power键失败，尝试其他方法...")
            if floating_page.open_floating_window():
                safe_log.info("✅ 通过备选方法打开浮窗成功")
                return floating_page.ensure_floating_window_ready()

            safe_log.error("❌ 无法打开Ella浮窗")
            return False

        except Exception as e:
            safe_log.error(f"确保浮窗就绪异常: {e}")
            return False

    def execute_floating_command_and_verify(self, floating_page: EllaFloatingPage, command: str,
                                          expected_keywords: Optional[List[str]] = None,
                                          verify_response: bool = True,
                                          response_timeout: int = None) -> tuple:
        """
        在浮窗中执行命令并验证结果的核心方法

        Args:
            floating_page: Ella浮窗页面实例
            command: 要执行的命令
            expected_keywords: 期望在响应中包含的关键词列表
            verify_response: 是否验证响应内容
            response_timeout: 响应等待超时时间，None使用默认值

        Returns:
            tuple: (success, response_texts, verification_result)
        """
        try:
            log.info(f"在浮窗中执行命令并验证: {command}")

            # 1. 确保浮窗就绪
            with allure.step("确保浮窗就绪"):
                if not self.ensure_floating_window_ready(floating_page):
                    raise AssertionError("浮窗未就绪")
                log.info("✅ 浮窗已就绪")

            # 2. 执行命令
            with allure.step(f"执行命令: {command}"):
                success = self._execute_floating_command_with_retry(floating_page, command)
                if not success:
                    raise AssertionError(f"命令执行失败: {command}")
                log.info(f"✅ 命令执行成功: {command}")

            # 3. 等待并获取响应
            with allure.step("等待并获取AI响应"):
                response_texts = self._wait_and_get_floating_response(
                    floating_page,
                    timeout=response_timeout or self._response_wait_timeout
                )
                log.info(f"✅ 获取到 {len(response_texts)} 个响应文本")

            # 4. 验证响应内容
            verification_result = True
            if verify_response and expected_keywords:
                with allure.step("验证响应内容"):
                    verification_result = self._verify_response_keywords(
                        response_texts, expected_keywords
                    )

            # 5. 记录测试结果
            with allure.step("记录测试结果"):
                self._record_floating_test_result(command, response_texts, verification_result)

            return success, response_texts, verification_result

        except Exception as e:
            log.error(f"浮窗命令执行和验证异常: {e}")
            # 截图记录错误状态
            self.take_screenshot(floating_page, "floating_command_error")
            raise

    def _execute_floating_command_with_retry(self, floating_page: EllaFloatingPage,
                                           command: str) -> bool:
        """
        带重试机制的浮窗命令执行

        Args:
            floating_page: Ella浮窗页面实例
            command: 要执行的命令

        Returns:
            bool: 是否执行成功
        """
        for attempt in range(self._command_retry_count):
            try:
                log.info(f"尝试执行命令 (第{attempt + 1}次): {command}")

                if floating_page.execute_text_command_in_floating(command):
                    log.info(f"✅ 命令执行成功 (第{attempt + 1}次尝试)")
                    return True
                else:
                    log.warning(f"⚠️ 命令执行失败 (第{attempt + 1}次尝试)")

                    # 如果不是最后一次尝试，等待一下再重试
                    if attempt < self._command_retry_count - 1:
                        time.sleep(1)
                        # 确保浮窗仍然可见
                        if not floating_page.is_floating_window_visible():
                            log.warning("浮窗不可见，尝试重新打开")
                            self.ensure_floating_window_ready(floating_page)

            except Exception as e:
                log.error(f"命令执行异常 (第{attempt + 1}次尝试): {e}")
                if attempt < self._command_retry_count - 1:
                    time.sleep(1)

        log.error(f"❌ 命令执行失败，已重试 {self._command_retry_count} 次")
        return False

    def _wait_and_get_floating_response(self, floating_page: EllaFloatingPage,
                                      timeout: int = 8) -> List[str]:
        """
        等待并获取浮窗响应

        Args:
            floating_page: Ella浮窗页面实例
            timeout: 等待超时时间

        Returns:
            List[str]: 响应文本列表
        """
        try:
            log.info(f"等待浮窗AI响应 (超时: {timeout}秒)")

            # 等待响应
            time.sleep(3)  # 给AI一些处理时间

            # 尝试等待响应出现
            if hasattr(floating_page, 'wait_for_floating_response'):
                floating_page.wait_for_floating_response(timeout=timeout)
            else:
                # 备选方案：简单等待
                time.sleep(min(timeout, 5))

            # 获取所有响应文本
            response_texts = floating_page.get_response_all_text()

            if response_texts:
                log.info(f"✅ 获取到响应文本: {len(response_texts)} 条")
                for i, text in enumerate(response_texts, 1):
                    log.debug(f"  响应 {i}: {text[:100]}...")
            else:
                log.warning("⚠️ 未获取到响应文本")

            return response_texts

        except Exception as e:
            log.error(f"获取浮窗响应异常: {e}")
            return []

    def _verify_response_keywords(self, response_texts: List[str],
                                expected_keywords: List[str]) -> bool:
        """
        验证响应中是否包含期望的关键词

        Args:
            response_texts: 响应文本列表
            expected_keywords: 期望的关键词列表

        Returns:
            bool: 验证是否通过
        """
        try:
            if not response_texts:
                log.warning("⚠️ 响应文本为空，无法验证关键词")
                return False

            if not expected_keywords:
                log.info("✅ 未指定期望关键词，跳过验证")
                return True

            # 将所有响应文本合并
            all_response_text = " ".join(response_texts).lower()

            found_keywords = []
            missing_keywords = []

            for keyword in expected_keywords:
                keyword_lower = keyword.lower()
                if keyword_lower in all_response_text:
                    found_keywords.append(keyword)
                    log.info(f"✅ 找到期望关键词: {keyword}")
                else:
                    missing_keywords.append(keyword)
                    log.warning(f"⚠️ 未找到期望关键词: {keyword}")

            # 记录验证结果
            verification_passed = len(missing_keywords) == 0

            if verification_passed:
                log.info(f"✅ 关键词验证通过，找到所有 {len(expected_keywords)} 个关键词")
            else:
                log.warning(f"⚠️ 关键词验证失败，缺失 {len(missing_keywords)} 个关键词: {missing_keywords}")

            # 附加验证结果到Allure报告
            verification_summary = f"""
关键词验证结果:
- 期望关键词: {expected_keywords}
- 找到关键词: {found_keywords}
- 缺失关键词: {missing_keywords}
- 验证结果: {'通过' if verification_passed else '失败'}
"""
            allure.attach(verification_summary.strip(),
                         name="关键词验证结果",
                         attachment_type=allure.attachment_type.TEXT)

            return verification_passed

        except Exception as e:
            log.error(f"关键词验证异常: {e}")
            return False

    def _record_floating_test_result(self, command: str, response_texts: List[str],
                                   verification_result: bool):
        """
        记录浮窗测试结果

        Args:
            command: 执行的命令
            response_texts: 响应文本列表
            verification_result: 验证结果
        """
        try:
            # 创建测试总结
            response_summary = "\n".join(response_texts) if response_texts else "无响应"

            summary = f"""
Ella浮窗测试总结
=====================================

执行命令: {command}
响应内容: {response_summary}
验证结果: {'通过' if verification_result else '失败'}
响应数量: {len(response_texts)}
测试时间: {time.strftime('%Y-%m-%d %H:%M:%S')}

测试状态: {'✅ 成功' if verification_result else '❌ 失败'}
"""

            # 附加到Allure报告
            allure.attach(summary.strip(),
                         name="浮窗测试总结",
                         attachment_type=allure.attachment_type.TEXT)

            # 附加响应内容
            if response_texts:
                allure.attach("\n".join(response_texts),
                             name="AI响应内容",
                             attachment_type=allure.attachment_type.TEXT)

            log.info(f"✅ 测试结果已记录: {command}")

        except Exception as e:
            log.error(f"记录测试结果异常: {e}")


class SimpleAskScreenTest(BaseAskScreenTest):
    """
    简化版Ella浮窗Ask Screen测试基类
    提供更简洁的测试方法，类似于SimpleEllaTest
    """

    def simple_floating_command_test(self, floating_page: EllaFloatingPage, command: str,
                                   expected_keywords: Optional[List[str]] = None,
                                   verify_response: bool = True,
                                   response_timeout: int = None) -> tuple:
        """
        简化的浮窗命令测试方法

        Args:
            floating_page: Ella浮窗页面实例
            command: 要执行的命令
            expected_keywords: 期望在响应中包含的关键词列表
            verify_response: 是否验证响应内容
            response_timeout: 响应等待超时时间

        Returns:
            tuple: (success, response_texts, verification_result)
        """
        with allure.step(f"执行浮窗命令: {command}"):
            success, response_texts, verification_result = self.execute_floating_command_and_verify(
                floating_page, command, expected_keywords, verify_response, response_timeout
            )

        with allure.step("截图记录测试完成状态"):
            self.take_screenshot(floating_page, "floating_test_completed")

        log.info(f"🎉 浮窗命令测试完成: {command}")
        return success, response_texts, verification_result

    def assert_floating_response_contains(self, response_texts: List[str],
                                        expected_keywords: List[str],
                                        match_all: bool = True):
        """
        断言浮窗响应包含期望的关键词

        Args:
            response_texts: 响应文本列表
            expected_keywords: 期望的关键词列表
            match_all: 是否需要匹配所有关键词，False表示匹配任意一个即可
        """
        if not response_texts:
            pytest.fail("响应文本为空")

        if not expected_keywords:
            log.info("✅ 未指定期望关键词，跳过断言")
            return

        all_response_text = " ".join(response_texts).lower()

        if match_all:
            # 需要匹配所有关键词
            missing_keywords = []
            for keyword in expected_keywords:
                if keyword.lower() not in all_response_text:
                    missing_keywords.append(keyword)

            if missing_keywords:
                pytest.fail(f"响应中缺失期望关键词: {missing_keywords}")
            else:
                log.info(f"✅ 响应包含所有期望关键词: {expected_keywords}")
        else:
            # 只需要匹配任意一个关键词
            found_any = any(keyword.lower() in all_response_text for keyword in expected_keywords)

            if not found_any:
                pytest.fail(f"响应中未找到任何期望关键词: {expected_keywords}")
            else:
                log.info(f"✅ 响应包含期望关键词中的至少一个: {expected_keywords}")

    def quick_floating_test(self, floating_page: EllaFloatingPage, command: str,
                          expected_keyword: str = None) -> List[str]:
        """
        快速浮窗测试方法 - 最简化的测试接口

        Args:
            floating_page: Ella浮窗页面实例
            command: 要执行的命令
            expected_keyword: 期望的单个关键词（可选）

        Returns:
            List[str]: 响应文本列表
        """
        expected_keywords = [expected_keyword] if expected_keyword else None

        success, response_texts, verification_result = self.simple_floating_command_test(
            floating_page, command, expected_keywords, verify_response=bool(expected_keyword)
        )

        if not success:
            pytest.fail(f"浮窗命令执行失败: {command}")

        if expected_keyword and not verification_result:
            pytest.fail(f"响应验证失败，未找到期望关键词: {expected_keyword}")

        return response_texts

    # ==================== 工具方法 ====================

    def take_floating_screenshot(self, floating_page: EllaFloatingPage, name: str):
        """为浮窗测试优化的截图方法"""
        try:
            screenshot_path = floating_page.screenshot(f"floating_{name}.png")
            allure.attach.file(screenshot_path, name=f"浮窗_{name}",
                             attachment_type=allure.attachment_type.PNG)
            log.debug(f"浮窗截图已保存: {name}")
        except Exception as e:
            log.warning(f"浮窗截图失败: {e}")

    def wait_for_floating_ready(self, floating_page: EllaFloatingPage, timeout: int = 10) -> bool:
        """等待浮窗就绪的便捷方法"""
        return self.ensure_floating_window_ready(floating_page)

    def get_floating_status_info(self, floating_page: EllaFloatingPage) -> dict:
        """获取浮窗状态信息"""
        try:
            status_info = {
                'visible': floating_page.is_floating_window_visible(),
                'input_ready': floating_page._ensure_floating_input_ready() if hasattr(floating_page, '_ensure_floating_input_ready') else False,
                'current_mode': floating_page._check_current_input_mode() if hasattr(floating_page, '_check_current_input_mode') else 'unknown'
            }

            log.debug(f"浮窗状态信息: {status_info}")
            return status_info

        except Exception as e:
            log.error(f"获取浮窗状态信息异常: {e}")
            return {'visible': False, 'input_ready': False, 'current_mode': 'unknown'}


# ==================== 示例测试类 ====================

@allure.epic("Ella浮窗测试")
@allure.feature("Ask Screen功能")
class TestAskScreenExample(SimpleAskScreenTest):
    """
    Ask Screen测试示例类
    展示如何使用SimpleAskScreenTest进行浮窗测试
    """

    @allure.story("基础浮窗命令测试")
    @allure.title("测试简单问候命令")
    @allure.description("测试在浮窗中执行简单问候命令并验证响应")
    def test_simple_greeting_command(self, ella_floating_page):
        """测试简单问候命令"""
        command = "hello"
        expected_keywords = ["hello", "hi", "good", "morning", "afternoon", "evening"]

        # 执行命令并验证
        success, response_texts, verification_result = self.simple_floating_command_test(
            ella_floating_page, command, expected_keywords, verify_response=True
        )

        # 断言结果
        assert success, f"命令执行失败: {command}"
        assert response_texts, "未获取到响应文本"

        # 可选：额外的断言
        self.assert_floating_response_contains(response_texts, expected_keywords, match_all=False)

    @allure.story("快速浮窗测试")
    @allure.title("测试快速命令执行")
    @allure.description("使用快速测试方法执行命令")
    def test_quick_floating_command(self, ella_floating_page):
        """测试快速命令执行"""
        command = "what time is it"
        expected_keyword = "time"

        # 使用快速测试方法
        response_texts = self.quick_floating_test(ella_floating_page, command, expected_keyword)

        # 验证响应
        assert response_texts, "未获取到响应文本"
        log.info(f"✅ 快速测试完成，获取到 {len(response_texts)} 个响应")

    @allure.story("浮窗状态检查")
    @allure.title("测试浮窗状态检查功能")
    @allure.description("测试浮窗状态检查和管理功能")
    def test_floating_window_status(self, ella_floating_page):
        """测试浮窗状态检查"""

        with allure.step("检查浮窗初始状态"):
            status_info = self.get_floating_status_info(ella_floating_page)
            log.info(f"浮窗状态信息: {status_info}")

        with allure.step("确保浮窗就绪"):
            assert self.wait_for_floating_ready(ella_floating_page), "浮窗未就绪"

        with allure.step("验证浮窗可见性"):
            assert ella_floating_page.is_floating_window_visible(), "浮窗不可见"

        with allure.step("截图记录浮窗状态"):
            self.take_floating_screenshot(ella_floating_page, "status_check")


if __name__ == '__main__':
    # 运行示例测试的命令
    # pytest testcases/test_ella/base_ask_screen_test.py::TestAskScreenExample -v --alluredir=reports/allure-results
    pass
