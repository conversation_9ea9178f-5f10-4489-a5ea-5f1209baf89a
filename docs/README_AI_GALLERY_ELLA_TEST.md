# AI Gallery与Ella浮窗集成测试

## 📋 项目概述

这是一个基于Page Object模式的Pytest测试脚本，用于测试AI Gallery选择图片后在Ella浮窗中输入指令的完整集成流程。

## 🎯 测试目标

实现以下完整测试流程：
1. **打开AI Gallery** - 启动AI Gallery应用
2. **选择第一张图片** - 在Photos页面选择第一张可用图片
3. **打开Ella浮窗** - 通过长按power键唤起Ella浮窗
4. **输入指令** - 在浮窗中输入指令 "add this number"
5. **验证响应** - 获取并验证Ella的AI响应

## 📁 文件结构

```
├── tests/test_ai_gallery_ella_integration.py    # 主测试文件
├── run_ai_gallery_ella_test.py                  # 测试运行脚本
├── examples/ai_gallery_ella_integration_example.py  # 使用示例
├── docs/AI_GALLERY_ELLA_INTEGRATION_TEST_GUIDE.md   # 详细指南
└── README_AI_GALLERY_ELLA_TEST.md               # 本文件
```

## 🚀 快速开始

### 1. 环境准备
- 确保Android设备已连接并可通过ADB访问
- 确保AI Gallery和Ella应用已安装
- 确保设备中有至少一张照片
- 确保Ella浮窗权限已开启

### 2. 运行完整测试
```bash
# 使用运行脚本（推荐）
python run_ai_gallery_ella_test.py

# 直接使用pytest
python -m pytest tests/test_ai_gallery_ella_integration.py -v
```

### 3. 运行特定测试
```bash
# 查看可用测试
python run_ai_gallery_ella_test.py --list

# 运行主要集成测试
python run_ai_gallery_ella_test.py -t test_ai_gallery_select_image_and_ella_command

# 运行基础功能测试
python run_ai_gallery_ella_test.py -t test_ai_gallery_basic_functionality
```

### 4. 生成测试报告
```bash
# 运行测试并自动打开Allure报告
python run_ai_gallery_ella_test.py --open
```

## 🔧 核心组件

### 页面对象
- **AiGalleryPhotosPage** (`pages/apps/ai_gallery/photos_page.py`)
  - AI Gallery Photos页面的完整封装
  - 支持应用启动、照片检测、照片点击等功能

- **EllaFloatingPage** (`pages/apps/ella/floating_page.py`)
  - Ella浮窗页面的完整封装
  - 支持浮窗唤起、命令执行、响应获取等功能

### 检测器
- **AiGalleryDetector** (`pages/base/detectors/ai_gallery_detector.py`)
  - AI Gallery应用检测器
  - 支持多种包名和关键词检测

## 📊 测试报告

测试会自动生成详细的Allure报告，包含：
- ✅ 每个测试步骤的执行状态
- 📸 关键步骤的自动截图
- 📝 Ella的响应内容记录
- 📋 完整的测试执行总结
- ❌ 失败时的详细错误信息

## 🛠️ 使用示例

### 基础示例
```python
from pages.apps.ai_gallery.photos_page import AiGalleryPhotosPage
from pages.apps.ella.floating_page import EllaFloatingPage

# 初始化页面对象
ai_gallery = AiGalleryPhotosPage()
ella_floating = EllaFloatingPage()

# 启动AI Gallery并选择图片
ai_gallery.start_app()
ai_gallery.wait_for_page_load()
ai_gallery.click_photo(index=0)

# 打开Ella浮窗并执行命令
ella_floating.trigger_ella_by_power_key(duration=3.0)
ella_floating.execute_text_command_in_floating("add this number")

# 获取响应
response = ella_floating.get_floating_response_text_all()
```

### 运行独立示例
```bash
# 运行完整集成示例
python examples/ai_gallery_ella_integration_example.py

# 仅测试AI Gallery
python examples/ai_gallery_ella_integration_example.py --test gallery

# 仅测试Ella浮窗
python examples/ai_gallery_ella_integration_example.py --test ella
```

## 🔍 故障排除

### 常见问题及解决方案

1. **AI Gallery启动失败**
   - 检查设备连接和应用安装状态
   - 确认应用包名 `com.gallery20` 是否正确

2. **找不到照片**
   - 确保设备中有照片文件
   - 检查AI Gallery的存储权限

3. **Ella浮窗无法打开**
   - 确认Ella应用已安装并启用
   - 检查浮窗权限设置
   - 尝试手动长按power键测试

4. **命令执行失败**
   - 检查网络连接（AI响应需要网络）
   - 确认输入法状态正常
   - 验证浮窗输入框可用性

## 📚 相关文档

- [详细测试指南](AI_GALLERY_ELLA_INTEGRATION_TEST_GUIDE.md)
- [AI Gallery页面使用指南](AI_GALLERY_PHOTOS_PAGE_GUIDE.md)
- [Ella浮窗页面使用指南](README_floating_page.md)

## 🎯 测试覆盖

- ✅ AI Gallery应用启动和页面加载
- ✅ Photos页面导航和状态检查
- ✅ 照片检测和选择功能
- ✅ Ella浮窗唤起和状态管理
- ✅ 文本命令输入和执行
- ✅ AI响应获取和内容验证
- ✅ 错误处理和异常恢复
- ✅ 资源清理和状态重置

## 🔄 持续改进

这个测试脚本采用模块化设计，可以轻松扩展：
- 添加更多测试场景
- 支持不同的指令类型
- 集成更多的验证点
- 优化错误处理逻辑

## 📞 技术支持

如果在使用过程中遇到问题，请：
1. 查看详细的测试日志
2. 检查Allure报告中的截图
3. 参考故障排除指南
4. 运行独立的示例脚本进行调试
