# Ella浮窗Ask Screen测试基类使用指南

## 📋 概述

`base_ask_screen_test.py` 模块提供了专门用于Ella浮窗测试的基类，参考 `SimpleEllaTest` 的设计模式，实现了在浮窗中输入指令和断言结果的完整封装。

## 🏗️ 架构设计

### 类层次结构
```
BaseEllaTest (基础测试类)
    ↓
BaseAskScreenTest (浮窗测试基类)
    ↓
SimpleAskScreenTest (简化版浮窗测试基类)
    ↓
TestAskScreenExample (示例测试类)
```

### 核心组件
- **BaseAskScreenTest**: 提供完整的浮窗测试功能
- **SimpleAskScreenTest**: 简化版本，提供更便捷的测试方法
- **EllaFloatingPage**: 浮窗页面对象（依赖）

## 🎯 主要功能

### 1. 浮窗管理
- ✅ 自动浮窗唤起和就绪检查
- ✅ 多种浮窗打开方式（长按power键、shell命令等）
- ✅ 浮窗状态监控和恢复

### 2. 命令执行
- ✅ 带重试机制的命令执行
- ✅ 自动输入模式切换（语音→文本）
- ✅ 多种发送方式支持

### 3. 响应处理
- ✅ 智能响应等待和获取
- ✅ 多种响应文本提取方式
- ✅ 响应内容过滤和清理

### 4. 结果验证
- ✅ 关键词匹配验证
- ✅ 灵活的断言方法
- ✅ 详细的验证报告

## 🚀 快速开始

### 基础使用示例

```python
from testcases.test_ella.base_ask_screen_test import SimpleAskScreenTest
import pytest
import allure

@allure.epic("我的浮窗测试")
class TestMyFloatingCommands(SimpleAskScreenTest):
    """我的浮窗命令测试"""

    def test_simple_command(self, ella_floating_page):
        """测试简单命令"""
        command = "hello"
        expected_keywords = ["hello", "hi", "good"]
        
        # 执行命令并验证
        success, response_texts, verification_result = self.simple_floating_command_test(
            ella_floating_page, command, expected_keywords
        )
        
        # 断言结果
        assert success, f"命令执行失败: {command}"
        assert verification_result, "响应验证失败"
```

### 快速测试方法

```python
def test_quick_command(self, ella_floating_page):
    """快速测试方法"""
    # 最简化的测试接口
    response_texts = self.quick_floating_test(
        ella_floating_page, 
        "what time is it", 
        expected_keyword="time"
    )
    
    assert response_texts, "未获取到响应"
```

## 📚 API参考

### BaseAskScreenTest 核心方法

#### `ensure_floating_window_ready(floating_page)`
确保浮窗就绪，包括打开浮窗和状态检查。

```python
# 确保浮窗就绪
ready = self.ensure_floating_window_ready(ella_floating_page)
assert ready, "浮窗未就绪"
```

#### `execute_floating_command_and_verify(floating_page, command, expected_keywords, verify_response, response_timeout)`
执行浮窗命令并验证结果的核心方法。

```python
success, response_texts, verification_result = self.execute_floating_command_and_verify(
    floating_page=ella_floating_page,
    command="tell me a joke",
    expected_keywords=["joke", "funny", "laugh"],
    verify_response=True,
    response_timeout=10
)
```

### SimpleAskScreenTest 简化方法

#### `simple_floating_command_test(floating_page, command, expected_keywords, verify_response, response_timeout)`
简化的浮窗命令测试方法。

```python
success, response_texts, verification_result = self.simple_floating_command_test(
    ella_floating_page, 
    "what's the weather", 
    expected_keywords=["weather", "temperature"]
)
```

#### `quick_floating_test(floating_page, command, expected_keyword)`
最简化的测试接口。

```python
response_texts = self.quick_floating_test(
    ella_floating_page, 
    "hello", 
    expected_keyword="hello"
)
```

#### `assert_floating_response_contains(response_texts, expected_keywords, match_all)`
断言响应包含期望关键词。

```python
# 需要匹配所有关键词
self.assert_floating_response_contains(
    response_texts, 
    ["weather", "temperature"], 
    match_all=True
)

# 只需匹配任意一个关键词
self.assert_floating_response_contains(
    response_texts, 
    ["hello", "hi", "good"], 
    match_all=False
)
```

## 🔧 配置选项

### 类级别配置

```python
class MyFloatingTest(SimpleAskScreenTest):
    # 浮窗操作超时时间
    _floating_window_timeout = 15
    
    # 响应等待超时时间
    _response_wait_timeout = 10
    
    # 命令执行重试次数
    _command_retry_count = 3
```

### 方法级别配置

```python
# 自定义响应超时时间
success, response_texts, verification_result = self.simple_floating_command_test(
    ella_floating_page, 
    "complex command", 
    response_timeout=20  # 20秒超时
)
```

## 📊 测试报告功能

### 自动生成的报告内容
- ✅ **测试步骤详情** - 每个步骤的执行状态
- ✅ **命令执行记录** - 完整的命令执行日志
- ✅ **响应内容记录** - AI的完整响应文本
- ✅ **关键词验证结果** - 详细的验证报告
- ✅ **错误截图** - 失败时的自动截图
- ✅ **测试总结** - 完整的测试执行总结

### 报告示例
```
Ella浮窗测试总结
=====================================

执行命令: tell me a joke
响应内容: Here's a joke for you: Why don't scientists trust atoms? Because they make up everything!
验证结果: 通过
响应数量: 1
测试时间: 2025-01-13 10:30:45

测试状态: ✅ 成功
```

## 🛠️ 高级用法

### 自定义验证逻辑

```python
def test_custom_verification(self, ella_floating_page):
    """自定义验证逻辑"""
    command = "calculate 2+2"
    
    # 执行命令但不自动验证
    success, response_texts, _ = self.simple_floating_command_test(
        ella_floating_page, command, verify_response=False
    )
    
    # 自定义验证逻辑
    response_text = " ".join(response_texts)
    assert "4" in response_text, "计算结果不正确"
    assert any(word in response_text.lower() for word in ["four", "4"]), "未找到答案"
```

### 批量命令测试

```python
def test_batch_commands(self, ella_floating_page):
    """批量命令测试"""
    commands = [
        ("hello", ["hello", "hi"]),
        ("what time is it", ["time"]),
        ("tell me a joke", ["joke", "funny"])
    ]
    
    for command, expected_keywords in commands:
        with allure.step(f"测试命令: {command}"):
            response_texts = self.quick_floating_test(
                ella_floating_page, command
            )
            
            # 验证响应
            self.assert_floating_response_contains(
                response_texts, expected_keywords, match_all=False
            )
```

### 状态监控和调试

```python
def test_with_status_monitoring(self, ella_floating_page):
    """带状态监控的测试"""
    
    # 获取初始状态
    initial_status = self.get_floating_status_info(ella_floating_page)
    log.info(f"初始状态: {initial_status}")
    
    # 执行命令
    response_texts = self.quick_floating_test(ella_floating_page, "hello")
    
    # 获取最终状态
    final_status = self.get_floating_status_info(ella_floating_page)
    log.info(f"最终状态: {final_status}")
    
    # 截图记录
    self.take_floating_screenshot(ella_floating_page, "test_completed")
```

## 🔍 故障排除

### 常见问题

1. **浮窗无法打开**
   ```python
   # 检查浮窗状态
   status = self.get_floating_status_info(ella_floating_page)
   log.info(f"浮窗状态: {status}")
   
   # 手动确保浮窗就绪
   assert self.ensure_floating_window_ready(ella_floating_page), "浮窗未就绪"
   ```

2. **命令执行失败**
   ```python
   # 使用重试机制
   success = self._execute_floating_command_with_retry(ella_floating_page, command)
   ```

3. **响应获取失败**
   ```python
   # 增加等待时间
   response_texts = self._wait_and_get_floating_response(
       ella_floating_page, timeout=15
   )
   ```

## 📝 最佳实践

1. **使用SimpleAskScreenTest** - 对于大多数测试场景
2. **合理设置超时时间** - 根据命令复杂度调整
3. **使用关键词验证** - 提高测试的可靠性
4. **充分利用截图功能** - 便于问题调试
5. **遵循Allure报告规范** - 提供清晰的测试文档

## 🔗 相关文档

- [Ella浮窗页面使用指南](README_floating_page.md)
- [BaseEllaTest使用指南](ELLA_TEST_SCRIPT_GUIDE.md)
- [Page Object模式最佳实践](../pages/README.md)
