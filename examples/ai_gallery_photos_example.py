"""
AI Gallery Photos页面使用示例
演示如何使用AiGalleryPhotosPage类进行页面操作
"""
import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from pages.apps.ai_gallery.photos_page import AiGalleryPhotosPage
from core.logger import log


def test_basic_operations():
    """测试基本操作"""
    log.info("=== 测试AI Gallery Photos页面基本操作 ===")
    
    photos_page = AiGalleryPhotosPage()
    
    try:
        # 启动应用
        if not photos_page.start_app():
            log.error("启动AI Gallery应用失败")
            return False
        
        # 等待页面加载
        if not photos_page.wait_for_page_load():
            log.error("页面加载失败")
            return False
        
        # 检查是否在Photos页面
        if photos_page.is_on_photos_page():
            log.info("✅ 当前在Photos页面")
        else:
            log.warning("当前不在Photos页面，尝试切换")
            photos_page.switch_to_photos()
        
        # 检查是否有照片
        if photos_page.has_photos():
            log.info("✅ 找到照片")
            
            # 点击第一张照片
            if photos_page.click_photo():
                log.info("✅ 点击照片成功")
            else:
                log.warning("点击照片失败")
        else:
            log.warning("未找到照片")
        
        log.info("✅ 基本操作测试完成")
        return True
        
    except Exception as e:
        log.error(f"基本操作测试异常: {e}")
        return False
    finally:
        # 停止应用
        photos_page.stop_app()


def test_navigation():
    """测试导航功能"""
    log.info("=== 测试AI Gallery导航功能 ===")
    
    photos_page = AiGalleryPhotosPage()
    
    try:
        # 启动应用
        if not photos_page.start_app():
            log.error("启动AI Gallery应用失败")
            return False
        
        # 等待页面加载
        if not photos_page.wait_for_page_load():
            log.error("页面加载失败")
            return False
        
        # 测试切换到不同标签页
        log.info("测试切换到Albums标签页")
        if photos_page.switch_to_albums():
            log.info("✅ 切换到Albums成功")
        
        log.info("测试切换到Memories标签页")
        if photos_page.switch_to_memories():
            log.info("✅ 切换到Memories成功")
        
        log.info("测试切换到Search标签页")
        if photos_page.switch_to_search():
            log.info("✅ 切换到Search成功")
        
        log.info("测试切换回Photos标签页")
        if photos_page.switch_to_photos():
            log.info("✅ 切换回Photos成功")
        
        log.info("✅ 导航功能测试完成")
        return True
        
    except Exception as e:
        log.error(f"导航功能测试异常: {e}")
        return False
    finally:
        # 停止应用
        photos_page.stop_app()


def test_scroll_operations():
    """测试滚动操作"""
    log.info("=== 测试AI Gallery滚动操作 ===")
    
    photos_page = AiGalleryPhotosPage()
    
    try:
        # 启动应用
        if not photos_page.start_app():
            log.error("启动AI Gallery应用失败")
            return False
        
        # 等待页面加载
        if not photos_page.wait_for_page_load():
            log.error("页面加载失败")
            return False
        
        # 确保在Photos页面
        if not photos_page.is_on_photos_page():
            photos_page.switch_to_photos()
        
        # 测试滚动操作
        log.info("测试向下滚动")
        if photos_page.scroll_photos_down():
            log.info("✅ 向下滚动成功")
        
        log.info("测试向上滚动")
        if photos_page.scroll_photos_up():
            log.info("✅ 向上滚动成功")
        
        log.info("✅ 滚动操作测试完成")
        return True
        
    except Exception as e:
        log.error(f"滚动操作测试异常: {e}")
        return False
    finally:
        # 停止应用
        photos_page.stop_app()


def test_toolbar_operations():
    """测试工具栏操作"""
    log.info("=== 测试AI Gallery工具栏操作 ===")
    
    photos_page = AiGalleryPhotosPage()
    
    try:
        # 启动应用
        if not photos_page.start_app():
            log.error("启动AI Gallery应用失败")
            return False
        
        # 等待页面加载
        if not photos_page.wait_for_page_load():
            log.error("页面加载失败")
            return False
        
        # 确保在Photos页面
        if not photos_page.is_on_photos_page():
            photos_page.switch_to_photos()
        
        # 测试进入多选模式
        log.info("测试进入多选模式")
        if photos_page.enter_select_mode():
            log.info("✅ 进入多选模式成功")
        
        # 测试点击更多选项
        log.info("测试点击更多选项")
        if photos_page.click_more_options():
            log.info("✅ 点击更多选项成功")
        
        log.info("✅ 工具栏操作测试完成")
        return True
        
    except Exception as e:
        log.error(f"工具栏操作测试异常: {e}")
        return False
    finally:
        # 停止应用
        photos_page.stop_app()


def main():
    """主函数"""
    log.info("开始AI Gallery Photos页面测试")
    
    # 运行所有测试
    tests = [
        test_basic_operations,
        test_navigation,
        test_scroll_operations,
        test_toolbar_operations
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
            log.info(f"测试 {test.__name__}: {'通过' if result else '失败'}")
        except Exception as e:
            log.error(f"测试 {test.__name__} 异常: {e}")
            results.append(False)
    
    # 输出总结
    passed = sum(results)
    total = len(results)
    log.info(f"测试总结: {passed}/{total} 个测试通过")
    
    if passed == total:
        log.info("🎉 所有测试通过！")
    else:
        log.warning(f"⚠️ 有 {total - passed} 个测试失败")


if __name__ == '__main__':
    main()
