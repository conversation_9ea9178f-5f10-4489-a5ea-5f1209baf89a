"""
AI Gallery与Ella浮窗集成示例
演示如何使用页面对象实现AI Gallery选择图片并在Ella浮窗中输入指令的完整流程
"""
import sys
import os
import time

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from pages.apps.ai_gallery.photos_page import AiGalleryPhotosPage
from pages.apps.ella.floating_page import EllaFloatingPage
from core.logger import log


def main():
    """主函数 - 演示完整的集成流程"""
    log.info("🚀 开始AI Gallery与Ella浮窗集成示例")
    
    # 初始化页面对象
    ai_gallery_page = None
    ella_floating_page = None
    
    try:
        # 步骤1: 初始化AI Gallery页面
        log.info("=" * 50)
        log.info("步骤1: 初始化并启动AI Gallery")
        log.info("=" * 50)
        
        ai_gallery_page = AiGalleryPhotosPage()
        
        # 启动AI Gallery应用
        if not ai_gallery_page.start_app():
            log.error("❌ AI Gallery应用启动失败")
            return False
        
        # 等待页面加载
        if not ai_gallery_page.wait_for_page_load(timeout=15):
            log.error("❌ AI Gallery页面加载失败")
            return False
        
        log.info("✅ AI Gallery应用启动成功")
        
        # 步骤2: 确保在Photos页面并选择第一张图片
        log.info("=" * 50)
        log.info("步骤2: 选择第一张图片")
        log.info("=" * 50)
        
        # 确保在Photos页面
        if not ai_gallery_page.is_on_photos_page():
            log.info("当前不在Photos页面，尝试切换")
            if not ai_gallery_page.switch_to_photos():
                log.error("❌ 切换到Photos页面失败")
                return False
        
        log.info("✅ 已确保在Photos页面")
        
        # 检查是否有照片
        if not ai_gallery_page.has_photos():
            log.error("❌ AI Gallery中没有找到照片")
            log.info("💡 请确保设备中有照片，或者手动添加一些测试照片")
            return False
        
        log.info("✅ 找到照片")
        
        # 点击第一张照片
        if not ai_gallery_page.click_photo(index=0):
            log.error("❌ 点击第一张照片失败")
            return False
        
        log.info("✅ 成功点击第一张照片")
        time.sleep(2)  # 等待照片加载
        
        # 步骤3: 初始化并打开Ella浮窗
        log.info("=" * 50)
        log.info("步骤3: 打开Ella浮窗")
        log.info("=" * 50)
        
        ella_floating_page = EllaFloatingPage()
        
        # 尝试通过长按power键唤起浮窗
        log.info("尝试通过长按power键唤起Ella浮窗...")
        success = ella_floating_page.trigger_ella_by_power_key(duration=3.0)
        
        if not success:
            log.warning("长按power键唤起浮窗失败，尝试其他方法...")
            success = ella_floating_page.open_floating_window()
        
        if not success:
            log.error("❌ 打开Ella浮窗失败")
            log.info("💡 请检查:")
            log.info("   - Ella应用是否已安装并启用")
            log.info("   - 浮窗权限是否已开启")
            log.info("   - 尝试手动长按power键测试")
            return False
        
        log.info("✅ 成功打开Ella浮窗")
        
        # 确保浮窗就绪
        if not ella_floating_page.ensure_floating_window_ready():
            log.error("❌ Ella浮窗未就绪")
            return False
        
        log.info("✅ Ella浮窗已就绪")
        
        # 步骤4: 在Ella浮窗中输入指令
        log.info("=" * 50)
        log.info("步骤4: 在Ella浮窗中输入指令")
        log.info("=" * 50)
        
        command = "add this number"
        log.info(f"准备执行命令: {command}")
        
        # 执行文本命令
        if not ella_floating_page.execute_text_command_in_floating(command):
            log.error(f"❌ 执行命令'{command}'失败")
            log.info("💡 请检查:")
            log.info("   - 浮窗输入框是否可用")
            log.info("   - 输入法状态是否正常")
            log.info("   - 网络连接是否正常（AI响应需要网络）")
            return False
        
        log.info(f"✅ 成功在Ella浮窗中执行命令: {command}")
        
        # 步骤5: 等待并获取Ella响应
        log.info("=" * 50)
        log.info("步骤5: 获取Ella响应")
        log.info("=" * 50)
        
        log.info("等待AI响应...")
        time.sleep(5)  # 等待AI处理
        
        # 获取响应文本
        response_texts = ella_floating_page.get_floating_response_text_all()
        
        if response_texts:
            response_text = " ".join(response_texts)
            log.info(f"✅ 获取到Ella响应: {response_text}")
        else:
            log.warning("⚠️ 未获取到Ella响应内容")
            response_text = "无响应"
        
        # 步骤6: 测试总结
        log.info("=" * 50)
        log.info("步骤6: 测试总结")
        log.info("=" * 50)
        
        summary = f"""
AI Gallery与Ella浮窗集成测试总结
=====================================

✅ 测试步骤:
1. ✅ 启动AI Gallery应用
2. ✅ 确保在Photos页面
3. ✅ 检查并选择第一张图片
4. ✅ 打开Ella浮窗
5. ✅ 在浮窗中输入指令
6. ✅ 获取AI响应

📝 执行详情:
- 执行命令: {command}
- Ella响应: {response_text}
- 测试结果: 成功

🎉 集成测试完成！
"""
        
        log.info(summary)
        return True
        
    except Exception as e:
        log.error(f"❌ 集成测试异常: {e}")
        return False
        
    finally:
        # 清理资源
        log.info("=" * 50)
        log.info("清理资源")
        log.info("=" * 50)
        
        try:
            # 关闭Ella浮窗
            if ella_floating_page and ella_floating_page.is_floating_window_visible():
                log.info("关闭Ella浮窗...")
                ella_floating_page.close_floating_window()
                log.info("✅ Ella浮窗已关闭")
        except Exception as e:
            log.warning(f"⚠️ 关闭Ella浮窗异常: {e}")
        
        try:
            # 停止AI Gallery应用
            if ai_gallery_page:
                log.info("停止AI Gallery应用...")
                ai_gallery_page.stop_app()
                log.info("✅ AI Gallery应用已停止")
        except Exception as e:
            log.warning(f"⚠️ 停止AI Gallery应用异常: {e}")


def test_ai_gallery_only():
    """仅测试AI Gallery功能"""
    log.info("🔍 测试AI Gallery基础功能")
    
    ai_gallery_page = AiGalleryPhotosPage()
    
    try:
        # 启动应用
        if not ai_gallery_page.start_app():
            log.error("❌ AI Gallery应用启动失败")
            return False
        
        if not ai_gallery_page.wait_for_page_load():
            log.error("❌ AI Gallery页面加载失败")
            return False
        
        log.info("✅ AI Gallery应用启动成功")
        
        # 检查照片
        has_photos = ai_gallery_page.has_photos()
        log.info(f"照片检测结果: {'有照片' if has_photos else '无照片'}")
        
        if has_photos:
            log.info("尝试点击第一张照片...")
            if ai_gallery_page.click_photo():
                log.info("✅ 点击照片成功")
            else:
                log.warning("⚠️ 点击照片失败")
        
        return True
        
    except Exception as e:
        log.error(f"❌ AI Gallery测试异常: {e}")
        return False
    finally:
        try:
            ai_gallery_page.stop_app()
        except:
            pass


def test_ella_floating_only():
    """仅测试Ella浮窗功能"""
    log.info("🔍 测试Ella浮窗基础功能")
    
    ella_floating_page = EllaFloatingPage()
    
    try:
        # 打开浮窗
        success = ella_floating_page.trigger_ella_by_power_key(duration=3.0)
        if not success:
            success = ella_floating_page.open_floating_window()
        
        if not success:
            log.error("❌ 打开Ella浮窗失败")
            return False
        
        log.info("✅ Ella浮窗打开成功")
        
        # 测试命令执行
        test_command = "hello"
        if ella_floating_page.execute_text_command_in_floating(test_command):
            log.info(f"✅ 测试命令'{test_command}'执行成功")
        else:
            log.warning(f"⚠️ 测试命令'{test_command}'执行失败")
        
        return True
        
    except Exception as e:
        log.error(f"❌ Ella浮窗测试异常: {e}")
        return False
    finally:
        try:
            if ella_floating_page.is_floating_window_visible():
                ella_floating_page.close_floating_window()
        except:
            pass


if __name__ == '__main__':
    import argparse
    
    parser = argparse.ArgumentParser(description="AI Gallery与Ella浮窗集成示例")
    parser.add_argument(
        "--test", 
        choices=["all", "gallery", "ella"], 
        default="all",
        help="选择要运行的测试: all(完整集成), gallery(仅AI Gallery), ella(仅Ella浮窗)"
    )
    
    args = parser.parse_args()
    
    if args.test == "all":
        success = main()
    elif args.test == "gallery":
        success = test_ai_gallery_only()
    elif args.test == "ella":
        success = test_ella_floating_only()
    
    if success:
        print("\n🎉 示例运行成功！")
    else:
        print("\n❌ 示例运行失败！")
        sys.exit(1)
