#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Ella浮窗Ask Screen测试运行脚本
提供便捷的测试执行方式和报告生成
"""
import os
import sys
import subprocess
import argparse
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from core.logger import log


def run_ask_screen_test(test_name=None, verbose=True, generate_report=True, open_report=False):
    """
    运行Ella浮窗Ask Screen测试
    
    Args:
        test_name: 指定测试方法名，None表示运行所有测试
        verbose: 是否显示详细输出
        generate_report: 是否生成Allure报告
        open_report: 是否自动打开报告
    """
    try:
        log.info("🚀 开始运行Ella浮窗Ask Screen测试")
        
        # 确保报告目录存在
        reports_dir = project_root / "reports"
        allure_results_dir = reports_dir / "allure-results"
        allure_report_dir = reports_dir / "allure-report"
        
        reports_dir.mkdir(exist_ok=True)
        allure_results_dir.mkdir(exist_ok=True)
        allure_report_dir.mkdir(exist_ok=True)
        
        # 构建pytest命令
        test_file = "testcases/test_ella/base_ask_screen_test.py"
        cmd = ["python", "-m", "pytest", test_file]
        
        # 添加测试方法过滤
        if test_name:
            cmd.extend(["-k", test_name])
        
        # 添加详细输出
        if verbose:
            cmd.append("-v")
        
        # 添加Allure报告生成
        if generate_report:
            cmd.extend(["--alluredir", str(allure_results_dir)])
        
        # 添加其他有用的选项
        cmd.extend([
            "--tb=short",  # 简化错误回溯
            "--strict-markers",  # 严格标记模式
            "-x"  # 遇到第一个失败就停止
        ])
        
        log.info(f"执行命令: {' '.join(cmd)}")
        
        # 运行测试
        result = subprocess.run(cmd, cwd=project_root, capture_output=False)
        
        if result.returncode == 0:
            log.info("✅ 测试执行成功")
            
            # 生成Allure报告
            if generate_report and allure_results_dir.exists():
                generate_allure_report(allure_results_dir, allure_report_dir, open_report)
                
        else:
            log.error(f"❌ 测试执行失败，退出码: {result.returncode}")
            
        return result.returncode == 0
        
    except Exception as e:
        log.error(f"❌ 运行测试异常: {e}")
        return False


def generate_allure_report(results_dir, report_dir, open_report=False):
    """生成Allure报告"""
    try:
        log.info("📊 生成Allure报告...")
        
        # 生成报告
        cmd = ["allure", "generate", str(results_dir), "-o", str(report_dir), "--clean"]
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            log.info(f"✅ Allure报告生成成功: {report_dir}")
            
            # 自动打开报告
            if open_report:
                open_allure_report(report_dir)
        else:
            log.error(f"❌ Allure报告生成失败: {result.stderr}")
            
    except FileNotFoundError:
        log.warning("⚠️ Allure命令未找到，请确保已安装Allure")
    except Exception as e:
        log.error(f"❌ 生成Allure报告异常: {e}")


def open_allure_report(report_dir):
    """打开Allure报告"""
    try:
        log.info("🌐 打开Allure报告...")
        cmd = ["allure", "open", str(report_dir)]
        subprocess.Popen(cmd)
        log.info("✅ Allure报告已在浏览器中打开")
    except Exception as e:
        log.error(f"❌ 打开Allure报告异常: {e}")


def list_available_tests():
    """列出可用的测试方法"""
    test_methods = [
        "test_simple_greeting_command",      # 简单问候命令测试
        "test_quick_floating_command",       # 快速命令测试
        "test_floating_window_status",       # 浮窗状态检查测试
        "TestAskScreenExample",              # 运行整个示例测试类
    ]
    
    print("\n📋 可用的测试方法:")
    for i, method in enumerate(test_methods, 1):
        print(f"  {i}. {method}")
    print()


def run_example_test():
    """运行示例测试"""
    try:
        log.info("🧪 运行Ask Screen测试示例")
        
        # 导入测试模块
        from testcases.test_ella.base_ask_screen_test import SimpleAskScreenTest
        from pages.apps.ella.floating_page import EllaFloatingPage
        
        # 创建测试实例
        test_instance = SimpleAskScreenTest()
        floating_page = EllaFloatingPage()
        
        try:
            # 确保浮窗就绪
            log.info("确保浮窗就绪...")
            if not test_instance.ensure_floating_window_ready(floating_page):
                log.error("❌ 浮窗未就绪")
                return False
            
            # 执行简单测试
            log.info("执行简单问候命令测试...")
            response_texts = test_instance.quick_floating_test(
                floating_page, "hello", expected_keyword="hello"
            )
            
            log.info(f"✅ 测试完成，获取到 {len(response_texts)} 个响应")
            for i, text in enumerate(response_texts, 1):
                log.info(f"  响应 {i}: {text[:100]}...")
            
            return True
            
        finally:
            # 清理
            try:
                if floating_page.is_floating_window_visible():
                    floating_page.close_floating_window()
            except:
                pass
                
    except Exception as e:
        log.error(f"❌ 运行示例测试异常: {e}")
        return False


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="Ella浮窗Ask Screen测试运行脚本")
    
    parser.add_argument(
        "-t", "--test", 
        help="指定要运行的测试方法名"
    )
    parser.add_argument(
        "-v", "--verbose", 
        action="store_true", 
        default=True,
        help="显示详细输出（默认启用）"
    )
    parser.add_argument(
        "--no-report", 
        action="store_true", 
        help="不生成Allure报告"
    )
    parser.add_argument(
        "--open", 
        action="store_true", 
        help="自动打开Allure报告"
    )
    parser.add_argument(
        "--list", 
        action="store_true", 
        help="列出可用的测试方法"
    )
    parser.add_argument(
        "--example", 
        action="store_true", 
        help="运行简单的示例测试"
    )
    
    args = parser.parse_args()
    
    if args.list:
        list_available_tests()
        return
    
    if args.example:
        success = run_example_test()
        if success:
            print("\n🎉 示例测试运行成功！")
        else:
            print("\n❌ 示例测试运行失败！")
            sys.exit(1)
        return
    
    # 运行pytest测试
    success = run_ask_screen_test(
        test_name=args.test,
        verbose=args.verbose,
        generate_report=not args.no_report,
        open_report=args.open
    )
    
    if success:
        print("\n🎉 测试运行完成！")
    else:
        print("\n❌ 测试运行失败！")
        sys.exit(1)


if __name__ == "__main__":
    main()
