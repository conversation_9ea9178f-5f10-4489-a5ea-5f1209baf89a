"""
AI Gallery与Ella浮窗集成测试
基于Page Object模式的Pytest脚本，测试AI Gallery选择图片后在Ella浮窗中输入指令的完整流程
"""
import pytest
import allure
import time
from core.logger import log


@allure.epic("AI Gallery与Ella集成")
@allure.feature("图片选择与浮窗指令")
class TestAiGalleryEllaIntegration:
    """AI Gallery与Ella浮窗集成测试类"""

    @pytest.fixture(scope="function")
    def ai_gallery_page(self):
        """AI Gallery页面fixture"""
        from pages.apps.ai_gallery.photos_page import AiGalleryPhotosPage
        
        page = AiGalleryPhotosPage()
        
        try:
            # 启动AI Gallery应用
            assert page.start_app(), "AI Gallery应用启动失败"
            assert page.wait_for_page_load(timeout=15), "AI Gallery页面加载失败"
            
            log.info("✅ AI Gallery应用启动成功")
            yield page
            
        except Exception as e:
            log.error(f"❌ AI Gallery应用启动异常: {e}")
            pytest.fail(f"AI Gallery应用启动异常: {e}")
        finally:
            # 清理
            try:
                page.stop_app()
            except Exception as e:
                log.warning(f"⚠️ 停止AI Gallery应用异常: {e}")

    @pytest.fixture(scope="function")
    def ella_floating_page(self):
        """Ella浮窗页面fixture"""
        from pages.apps.ella.floating_page import EllaFloatingPage
        
        page = EllaFloatingPage()
        
        try:
            log.info("✅ Ella浮窗页面初始化成功")
            yield page
            
        except Exception as e:
            log.error(f"❌ Ella浮窗页面初始化异常: {e}")
            pytest.fail(f"Ella浮窗页面初始化异常: {e}")
        finally:
            # 清理：关闭浮窗
            try:
                if page.is_floating_window_visible():
                    page.close_floating_window()
            except Exception as e:
                log.warning(f"⚠️ 关闭Ella浮窗异常: {e}")

    @allure.story("AI Gallery图片选择与Ella浮窗指令")
    @allure.title("测试AI Gallery选择第一张图片并在Ella浮窗中输入指令")
    @allure.description("完整测试流程：1.打开AI Gallery 2.选择第一张图片 3.打开Ella浮窗 4.输入指令'add this number'")
    def test_ai_gallery_select_image_and_ella_command(self, ai_gallery_page, ella_floating_page):
        """测试AI Gallery选择图片并在Ella浮窗中输入指令的完整流程"""
        
        # 步骤1: 确保在AI Gallery Photos页面
        with allure.step("步骤1: 确保在AI Gallery Photos页面"):
            if not ai_gallery_page.is_on_photos_page():
                assert ai_gallery_page.switch_to_photos(), "切换到Photos页面失败"
            log.info("✅ 已确保在AI Gallery Photos页面")
            
            # 截图记录当前状态
            self._take_screenshot(ai_gallery_page, "ai_gallery_photos_page")

        # 步骤2: 检查是否有照片并选择第一张
        with allure.step("步骤2: 检查是否有照片并选择第一张"):
            assert ai_gallery_page.has_photos(), "AI Gallery中没有找到照片"
            log.info("✅ AI Gallery中找到照片")
            
            # 点击第一张照片
            assert ai_gallery_page.click_photo(index=0), "点击第一张照片失败"
            log.info("✅ 成功点击第一张照片")
            
            # 等待照片加载
            time.sleep(2)
            
            # 截图记录选择的照片
            self._take_screenshot(ai_gallery_page, "selected_first_photo")

        # 步骤3: 打开Ella浮窗
        with allure.step("步骤3: 打开Ella浮窗"):
            # 优先使用长按power键方式唤起浮窗
            success = ella_floating_page.trigger_ella_by_power_key(duration=3.0)
            
            if not success:
                log.warning("长按power键唤起浮窗失败，尝试其他方法")
                success = ella_floating_page.open_floating_window()
            
            assert success, "打开Ella浮窗失败"
            log.info("✅ 成功打开Ella浮窗")
            
            # 确保浮窗就绪
            assert ella_floating_page.ensure_floating_window_ready(), "Ella浮窗未就绪"
            log.info("✅ Ella浮窗已就绪")
            
            # 截图记录浮窗状态
            self._take_screenshot(ella_floating_page, "ella_floating_window_opened")

        # 步骤4: 在Ella浮窗中输入指令
        with allure.step("步骤4: 在Ella浮窗中输入指令 'add this number'"):
            command = "add this number"
            
            # 执行文本命令
            assert ella_floating_page.execute_text_command_in_floating(command), f"执行命令'{command}'失败"
            log.info(f"✅ 成功在Ella浮窗中执行命令: {command}")
            
            # 等待AI响应
            time.sleep(3)
            
            # 截图记录命令执行后的状态
            self._take_screenshot(ella_floating_page, "command_executed")

        # 步骤5: 获取并验证Ella响应
        with allure.step("步骤5: 获取并验证Ella响应"):
            # 获取响应文本
            response_texts = ella_floating_page.get_floating_response_text_all()
            
            if response_texts:
                response_text = " ".join(response_texts)
                log.info(f"✅ 获取到Ella响应: {response_text}")
                
                # 将响应附加到Allure报告
                allure.attach(response_text, name="Ella响应内容", attachment_type=allure.attachment_type.TEXT)
            else:
                log.warning("⚠️ 未获取到Ella响应内容")
                response_text = "无响应"
            
            # 截图记录最终状态
            self._take_screenshot(ella_floating_page, "final_response_state")

        # 步骤6: 创建测试总结
        with allure.step("步骤6: 创建测试总结"):
            summary = self._create_test_summary(command, response_text)
            allure.attach(summary, name="测试总结", attachment_type=allure.attachment_type.TEXT)
            log.info("✅ 测试总结已创建")

    def _take_screenshot(self, page_instance, name: str):
        """截图并附加到Allure报告"""
        try:
            screenshot_path = page_instance.screenshot(f"{name}.png")
            allure.attach.file(screenshot_path, name=name, attachment_type=allure.attachment_type.PNG)
            log.debug(f"截图已保存: {name}")
        except Exception as e:
            log.warning(f"截图失败: {e}")

    def _create_test_summary(self, command: str, response_text: str) -> str:
        """创建测试总结"""
        summary = f"""
AI Gallery与Ella浮窗集成测试总结
=====================================

测试步骤:
1. ✅ 打开AI Gallery应用
2. ✅ 确保在Photos页面
3. ✅ 检查并选择第一张图片
4. ✅ 打开Ella浮窗
5. ✅ 在浮窗中输入指令

执行命令: {command}
Ella响应: {response_text}
测试结果: 成功

测试时间: {time.strftime('%Y-%m-%d %H:%M:%S')}
"""
        return summary.strip()

    @allure.story("AI Gallery基础功能")
    @allure.title("测试AI Gallery应用启动和照片检测")
    @allure.description("验证AI Gallery应用能够正常启动并检测到照片")
    def test_ai_gallery_basic_functionality(self, ai_gallery_page):
        """测试AI Gallery基础功能"""
        
        with allure.step("验证AI Gallery应用已启动"):
            assert ai_gallery_page.is_on_photos_page(), "未在Photos页面"
            log.info("✅ AI Gallery应用已正常启动")

        with allure.step("验证照片检测功能"):
            has_photos = ai_gallery_page.has_photos()
            log.info(f"照片检测结果: {'有照片' if has_photos else '无照片'}")
            
            # 即使没有照片也不应该失败，只是记录状态
            allure.attach(f"照片状态: {'有照片' if has_photos else '无照片'}", 
                         name="照片检测结果", attachment_type=allure.attachment_type.TEXT)

    @allure.story("Ella浮窗基础功能")
    @allure.title("测试Ella浮窗打开和命令执行")
    @allure.description("验证Ella浮窗能够正常打开并执行基础命令")
    def test_ella_floating_basic_functionality(self, ella_floating_page):
        """测试Ella浮窗基础功能"""
        
        with allure.step("打开Ella浮窗"):
            success = ella_floating_page.trigger_ella_by_power_key(duration=3.0)
            if not success:
                success = ella_floating_page.open_floating_window()
            assert success, "打开Ella浮窗失败"
            log.info("✅ Ella浮窗打开成功")

        with allure.step("验证浮窗状态"):
            assert ella_floating_page.is_floating_window_visible(), "浮窗不可见"
            assert ella_floating_page.ensure_floating_window_ready(), "浮窗未就绪"
            log.info("✅ Ella浮窗状态正常")

        with allure.step("执行测试命令"):
            test_command = "hello"
            success = ella_floating_page.execute_text_command_in_floating(test_command)
            assert success, f"执行测试命令'{test_command}'失败"
            log.info(f"✅ 测试命令'{test_command}'执行成功")


if __name__ == '__main__':
    # 运行测试的示例命令
    # pytest tests/test_ai_gallery_ella_integration.py -v --allure-dir=reports/allure-results
    pass
