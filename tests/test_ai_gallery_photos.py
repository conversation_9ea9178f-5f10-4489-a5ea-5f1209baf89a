"""
AI Gallery Photos页面测试用例
使用pytest框架测试AiGalleryPhotosPage类的功能
"""
import pytest
import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from pages.apps.ai_gallery.photos_page import AiGalleryPhotosPage
from core.logger import log


@pytest.fixture(scope="function")
def photos_page():
    """创建AI Gallery Photos页面实例"""
    page = AiGalleryPhotosPage()
    
    # 启动应用
    if not page.start_app():
        pytest.fail("启动AI Gallery应用失败")
    
    # 等待页面加载
    if not page.wait_for_page_load():
        pytest.fail("页面加载失败")
    
    yield page
    
    # 清理：停止应用
    page.stop_app()


class TestAiGalleryPhotosPage:
    """AI Gallery Photos页面测试类"""

    def test_app_startup(self, photos_page):
        """测试应用启动"""
        log.info("测试AI Gallery应用启动")
        
        # 检查是否成功启动到Photos页面
        assert photos_page.is_on_photos_page(), "应用未启动到Photos页面"
        log.info("✅ 应用启动测试通过")

    def test_page_elements_exist(self, photos_page):
        """测试页面元素是否存在"""
        log.info("测试页面关键元素是否存在")
        
        # 检查底部导航栏
        assert photos_page.foot_opt_bar.is_exists(), "底部导航栏不存在"
        
        # 检查Photos标签
        assert photos_page.photos_tab.is_exists(), "Photos标签不存在"
        
        # 检查应用栏
        assert photos_page.app_bar_layout.is_exists(), "应用栏不存在"
        
        log.info("✅ 页面元素存在性测试通过")

    def test_navigation_tabs(self, photos_page):
        """测试导航标签切换"""
        log.info("测试导航标签切换功能")
        
        # 确保当前在Photos页面
        if not photos_page.is_on_photos_page():
            photos_page.switch_to_photos()
        
        # 测试切换到Albums
        result = photos_page.switch_to_albums()
        assert result, "切换到Albums失败"
        
        # 测试切换到Memories
        result = photos_page.switch_to_memories()
        assert result, "切换到Memories失败"
        
        # 测试切换到Search
        result = photos_page.switch_to_search()
        assert result, "切换到Search失败"
        
        # 切换回Photos
        result = photos_page.switch_to_photos()
        assert result, "切换回Photos失败"
        
        # 验证确实回到了Photos页面
        assert photos_page.is_on_photos_page(), "未成功切换回Photos页面"
        
        log.info("✅ 导航标签切换测试通过")

    def test_photo_interaction(self, photos_page):
        """测试照片交互功能"""
        log.info("测试照片交互功能")
        
        # 确保在Photos页面
        if not photos_page.is_on_photos_page():
            photos_page.switch_to_photos()
        
        # 检查是否有照片
        if photos_page.has_photos():
            # 测试点击照片
            result = photos_page.click_photo()
            assert result, "点击照片失败"
            log.info("✅ 照片点击测试通过")
        else:
            log.warning("⚠️ 设备中没有照片，跳过照片交互测试")
            pytest.skip("设备中没有照片")

    def test_scroll_operations(self, photos_page):
        """测试滚动操作"""
        log.info("测试滚动操作")
        
        # 确保在Photos页面
        if not photos_page.is_on_photos_page():
            photos_page.switch_to_photos()
        
        # 测试向下滚动
        result = photos_page.scroll_photos_down()
        assert result, "向下滚动失败"
        
        # 测试向上滚动
        result = photos_page.scroll_photos_up()
        assert result, "向上滚动失败"
        
        log.info("✅ 滚动操作测试通过")

    def test_toolbar_operations(self, photos_page):
        """测试工具栏操作"""
        log.info("测试工具栏操作")
        
        # 确保在Photos页面
        if not photos_page.is_on_photos_page():
            photos_page.switch_to_photos()
        
        # 测试进入多选模式
        if photos_page.select_mode_button.is_exists():
            result = photos_page.enter_select_mode()
            assert result, "进入多选模式失败"
            log.info("✅ 多选模式测试通过")
        else:
            log.warning("⚠️ 多选模式按钮不存在，跳过测试")
        
        # 测试更多选项
        if photos_page.more_options_button.is_exists():
            result = photos_page.click_more_options()
            assert result, "点击更多选项失败"
            log.info("✅ 更多选项测试通过")
        else:
            log.warning("⚠️ 更多选项按钮不存在，跳过测试")

    def test_page_state_checks(self, photos_page):
        """测试页面状态检查"""
        log.info("测试页面状态检查功能")
        
        # 测试Photos页面状态检查
        is_on_photos = photos_page.is_on_photos_page()
        log.info(f"当前是否在Photos页面: {is_on_photos}")
        
        # 测试照片存在性检查
        has_photos = photos_page.has_photos()
        log.info(f"是否有照片: {has_photos}")
        
        # 这些检查不应该抛出异常
        assert isinstance(is_on_photos, bool), "is_on_photos_page应该返回布尔值"
        assert isinstance(has_photos, bool), "has_photos应该返回布尔值"
        
        log.info("✅ 页面状态检查测试通过")

    @pytest.mark.parametrize("tab_name,switch_method", [
        ("Albums", "switch_to_albums"),
        ("Memories", "switch_to_memories"),
        ("Search", "switch_to_search"),
        ("Photos", "switch_to_photos"),
    ])
    def test_individual_tab_switch(self, photos_page, tab_name, switch_method):
        """参数化测试各个标签页切换"""
        log.info(f"测试切换到{tab_name}标签页")
        
        # 获取切换方法
        switch_func = getattr(photos_page, switch_method)
        
        # 执行切换
        result = switch_func()
        assert result, f"切换到{tab_name}失败"
        
        log.info(f"✅ {tab_name}标签页切换测试通过")

    def test_error_handling(self, photos_page):
        """测试错误处理"""
        log.info("测试错误处理机制")
        
        # 这些方法应该能够优雅地处理错误情况
        # 即使元素不存在也不应该抛出异常
        
        try:
            photos_page.click_photo(999)  # 尝试点击不存在的照片
            photos_page.enter_select_mode()
            photos_page.click_more_options()
            log.info("✅ 错误处理测试通过")
        except Exception as e:
            pytest.fail(f"错误处理测试失败，抛出了异常: {e}")


class TestAiGalleryPhotosPagePerformance:
    """AI Gallery Photos页面性能测试类"""

    def test_page_load_time(self):
        """测试页面加载时间"""
        import time
        
        log.info("测试页面加载时间")
        
        photos_page = AiGalleryPhotosPage()
        
        try:
            # 记录启动时间
            start_time = time.time()
            
            # 启动应用
            assert photos_page.start_app(), "应用启动失败"
            
            # 等待页面加载
            assert photos_page.wait_for_page_load(), "页面加载失败"
            
            # 计算加载时间
            load_time = time.time() - start_time
            
            log.info(f"页面加载时间: {load_time:.2f}秒")
            
            # 页面加载时间应该在合理范围内（比如30秒以内）
            assert load_time < 30, f"页面加载时间过长: {load_time:.2f}秒"
            
            log.info("✅ 页面加载时间测试通过")
            
        finally:
            photos_page.stop_app()

    def test_element_response_time(self, photos_page):
        """测试元素响应时间"""
        import time
        
        log.info("测试元素响应时间")
        
        # 测试几个关键元素的响应时间
        elements_to_test = [
            ("底部导航栏", photos_page.foot_opt_bar),
            ("Photos标签", photos_page.photos_tab),
            ("应用栏", photos_page.app_bar_layout),
        ]
        
        for element_name, element in elements_to_test:
            start_time = time.time()
            exists = element.is_exists()
            response_time = time.time() - start_time
            
            log.info(f"{element_name}响应时间: {response_time:.3f}秒, 存在: {exists}")
            
            # 元素检查响应时间应该在合理范围内（比如5秒以内）
            assert response_time < 5, f"{element_name}响应时间过长: {response_time:.3f}秒"
        
        log.info("✅ 元素响应时间测试通过")


if __name__ == '__main__':
    # 运行测试
    pytest.main([__file__, "-v", "--tb=short"])
